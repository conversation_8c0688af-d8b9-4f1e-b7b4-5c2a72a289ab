#!/bin/bash

# 考试系统Docker镜像导入脚本

set -e

echo "🚀 开始导入考试系统Docker镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 检查当前目录是否有镜像文件
echo "🔍 检查镜像文件..."
IMAGE_FILES=(exam-api.tar.gz exam-vue.tar.gz exam-nginx.tar.gz)
MISSING_FILES=()

for file in "${IMAGE_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        MISSING_FILES+=("$file")
    fi
done

if [ ${#MISSING_FILES[@]} -gt 0 ]; then
    echo "❌ 缺少以下镜像文件："
    for file in "${MISSING_FILES[@]}"; do
        echo "   - $file"
    done
    echo ""
    echo "请确保以下文件在当前目录："
    echo "   - exam-api.tar.gz    (Spring Boot后端镜像)"
    echo "   - exam-vue.tar.gz    (Vue.js前端镜像)"
    echo "   - exam-nginx.tar.gz  (Nginx反向代理镜像)"
    exit 1
fi

echo "✅ 所有镜像文件检查完成"
echo ""

# 显示文件大小
echo "📊 镜像文件信息："
ls -lh *.tar.gz

echo ""
echo "📦 开始导入镜像..."

# 导入后端API镜像
echo "🔧 导入Spring Boot后端镜像..."
if gunzip -c exam-api.tar.gz | docker load; then
    echo "✅ 后端镜像导入成功"
else
    echo "❌ 后端镜像导入失败"
    exit 1
fi

# 导入前端Vue镜像
echo "🔧 导入Vue.js前端镜像..."
if gunzip -c exam-vue.tar.gz | docker load; then
    echo "✅ 前端镜像导入成功"
else
    echo "❌ 前端镜像导入失败"
    exit 1
fi

# 导入Nginx镜像
echo "🔧 导入Nginx反向代理镜像..."
if gunzip -c exam-nginx.tar.gz | docker load; then
    echo "✅ Nginx镜像导入成功"
else
    echo "❌ Nginx镜像导入失败"
    exit 1
fi

echo ""
echo "📋 检查导入的镜像："
docker images | grep -E "(exam|yfexam)" | head -10

echo ""
echo "🎉 所有镜像导入完成！"
echo ""
echo "📋 下一步操作："
echo "1. 确保有docker-compose.yml文件"
echo "2. 创建必要的目录结构："
echo "   mkdir -p mysql/data mysql/init"
echo "3. 复制数据库初始化脚本到 mysql/init/ 目录"
echo "4. 启动服务："
echo "   docker-compose up -d"
echo ""
echo "🔍 服务检查命令："
echo "   docker-compose ps"
echo "   docker-compose logs -f"
echo ""
echo "✨ 部署完成后访问 http://your-server-ip 即可使用考试系统！"

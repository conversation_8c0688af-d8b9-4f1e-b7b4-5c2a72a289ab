#!/bin/bash

# 考试系统Docker构建脚本

set -e

usage() {
    cat <<EOF
Usage: $0 [options]

Options:
  -c    干净构建，不使用 Docker 缓存层
  -e    构建完成后自动导出镜像到 images/ 目录
  -h    显示此帮助信息

示例：
  $0 -c           # 干净构建
  $0 -e           # 构建并导出镜像
  $0 -c -e        # 干净构建并导出镜像
EOF
    exit 1
}

# 解析选项
CLEAN_BUILD=false
EXPORT_IMAGES=false
while getopts "ceh" opt; do
    case $opt in
        c) CLEAN_BUILD=true ;;  
        e) EXPORT_IMAGES=true ;;  
        h) usage ;;  
        *) usage ;;    
    esac
done

# 构建参数初始化
COMPOSE_OPTS=""

echo "🚀 开始构建考试系统Docker镜像..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 进入脚本所在目录（docker-compose.yml 所在）
cd "$(dirname "$0")"

# 如果需要干净构建，则添加 --no-cache
if [ "$CLEAN_BUILD" = true ]; then
    echo "🔄 使用 --no-cache 进行干净构建"
    COMPOSE_OPTS="--no-cache"
fi

# 开始构建

echo "📦 构建MySQL容器..."
echo "✅ MySQL 使用官方镜像，无需本地构建"

echo "📦 构建 Spring Boot 后端容器..."
docker-compose build $COMPOSE_OPTS exam-api

echo "📦 构建 Vue.js 前端容器..."
docker-compose build $COMPOSE_OPTS exam-vue

echo "📦 构建 Nginx 反向代理容器..."
docker-compose build $COMPOSE_OPTS nginx

# 显示构建结果
 echo "🎉 所有镜像构建完成！"
 echo ""
 echo "📋 构建的镜像："
 docker images | grep -E "(exam|mysql)" | head -10

# 如果需要导出，则执行导出流程
if [ "$EXPORT_IMAGES" = true ]; then
    echo "📦 导出镜像到 images/ 目录..."
    mkdir -p images

    echo "  - 导出 exam-exam-api 镜像"
    docker save exam-exam-api:latest | gzip > images/exam-api.tar.gz
    echo "    完成： images/exam-api.tar.gz"

    echo "  - 导出 exam-exam-vue 镜像"
    docker save exam-exam-vue:latest | gzip > images/exam-vue.tar.gz
    echo "    完成： images/exam-vue.tar.gz"

    echo "  - 导出 exam-nginx 镜像"
    docker save exam-nginx:latest | gzip > images/exam-nginx.tar.gz
    echo "    完成： images/exam-nginx.tar.gz"

    echo "✅ 镜像导出完成"
fi

# 最后提示
 echo ""
 echo "🚀 下一步启动服务："
 echo "   ./start.sh"
 echo "🚀 全部完成"

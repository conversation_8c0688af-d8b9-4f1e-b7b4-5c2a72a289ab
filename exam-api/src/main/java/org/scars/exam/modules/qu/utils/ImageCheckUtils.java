package org.scars.exam.modules.qu.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.scars.exam.ability.upload.config.UploadConfig;
import org.scars.exam.core.exception.ServiceException;

@Component
public class ImageCheckUtils {

    @Autowired
    private UploadConfig conf;

    /**
     * 进行图片校验！
     * @param image
     * @param throwMsg
     */
    public void checkImage(String image, String throwMsg) {

        if(StringUtils.isBlank(image)){
            return;
        }

        // 校验图片地址 - 支持相对路径和完整URL
        if(!isValidImagePath(image)){
            throw new ServiceException(throwMsg);
        }
    }

    /**
     * 检查是否为有效的图片路径
     * @param image 图片路径
     * @return 是否有效
     */
    private boolean isValidImagePath(String image) {
        if (StringUtils.isBlank(image)) {
            return false;
        }

        // 支持相对路径格式（推荐）
        String pathPrefix = conf.getAccessPathPrefix();
        if (image.startsWith(pathPrefix)) {
            return true;
        }

        // 兼容旧的完整URL格式
        if (conf.getUrl() != null && image.startsWith(conf.getUrl())) {
            return true;
        }

        // 支持外部图片链接（http/https开头）
        if (image.startsWith("http://") || image.startsWith("https://")) {
            return true;
        }

        return false;
    }
}

# 开发环境配置文件
spring:
  # 数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: 123456
    # druid相关配置
    druid:
      max-active: 5000
      initial-size: 20
      min-idle: 5
      async-init: true
      # 监控统计
      filters: stat,wall
      filter:
        stat:
          log-slow-sql: true
          slow-sql-millis: 5000
        wall:
          config:
            create-table-allow: false
            alter-table-allow: false
            drop-table-allow: false
            truncate-allow: false

  # 定时任务配置
  quartz:
    # 数据库方式
    job-store-type: jdbc
    # quartz 相关属性配置
    properties:
      org:
        quartz:
          scheduler:
            instanceName: eamScheduler
            instanceId: AUTO
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            clusterCheckinInterval: 10000
            useProperties: false
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true

# 文件上传配置
conf:
  upload:
    # 物理文件存储位置，使用项目相对路径，便于备份和部署
    dir: ./upload/
    # 相对路径前缀（推荐使用，支持动态域名）
    path-prefix: /upload/file/
    # 访问地址（兼容旧版本，已废弃）
    # url: http://localhost:8101/upload/file/
    # 允许上传的文件后缀
    allow-extensions: jpg,jpeg,png

# 开启文档
swagger:
  enable: true

logging:
  level:
    root: debug
  path: logs/${spring.application.name}/

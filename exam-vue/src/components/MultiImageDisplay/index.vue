<template>
  <div class="multi-image-display">
    <div v-if="imageUrls.length === 0" class="no-image">
      暂无图片
    </div>
    <div v-else class="image-container">
      <el-image
        v-for="(url, index) in imageUrls"
        :key="index"
        :src="url"
        :style="imageStyle"
        fit="cover"
        :preview-src-list="imageUrls"
        :initial-index="index"
        class="image-item"
      >
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
      </el-image>
    </div>
  </div>
</template>

<script>
import { getImageUrls } from '@/utils/imageUtils'

export default {
  name: 'MultiImageDisplay',
  props: {
    // 图片URL字符串，多个URL用逗号分隔
    imageString: {
      type: String,
      default: ''
    },
    // 图片样式
    width: {
      type: String,
      default: '100px'
    },
    height: {
      type: String,
      default: '100px'
    },
    // 最大显示数量
    maxCount: {
      type: Number,
      default: 5
    }
  },
  computed: {
    imageUrls() {
      if (!this.imageString) return []

      // 使用工具函数处理图片URL，支持动态域名拼接
      const urls = getImageUrls(this.imageString)
      return urls.slice(0, this.maxCount)
    },
    imageStyle() {
      return {
        width: this.width,
        height: this.height,
        margin: '2px'
      }
    }
  }
}
</script>

<style scoped>
.multi-image-display {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.no-image {
  color: #999;
  font-size: 12px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.image-item {
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
}
</style>

/**
 * 图片URL处理工具函数
 * 支持动态域名拼接，解决局域网环境IP变化问题
 * 
 * <AUTHOR> 4.0 sonnet
 */

/**
 * 获取当前访问的基础URL
 * @returns {string} 基础URL，如：http://*************:8101
 */
function getBaseUrl() {
  // 优先使用环境变量配置的API地址
  if (process.env.VUE_APP_BASE_API) {
    return process.env.VUE_APP_BASE_API
  }
  
  // 如果没有配置，使用当前页面的origin
  return window.location.origin
}

/**
 * 处理图片URL，支持相对路径自动拼接域名
 * @param {string} imagePath 图片路径
 * @returns {string} 完整的图片URL
 */
export function getImageUrl(imagePath) {
  if (!imagePath) {
    return ''
  }

  // 如果已经是完整的URL（http/https开头），直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath
  }

  // 如果是相对路径，拼接基础URL
  if (imagePath.startsWith('/')) {
    return getBaseUrl() + imagePath
  }

  // 如果路径不是以/开头，添加/upload/file/前缀
  return getBaseUrl() + '/upload/file/' + imagePath
}

/**
 * 处理多个图片URL（逗号分隔的字符串）
 * @param {string} imageString 图片URL字符串，多个URL用逗号分隔
 * @returns {Array<string>} 处理后的图片URL数组
 */
export function getImageUrls(imageString) {
  if (!imageString) {
    return []
  }

  return imageString
    .split(',')
    .map(url => url.trim())
    .filter(url => url)
    .map(url => getImageUrl(url))
}

/**
 * 检查是否为有效的图片路径
 * @param {string} imagePath 图片路径
 * @returns {boolean} 是否为有效路径
 */
export function isValidImagePath(imagePath) {
  if (!imagePath) {
    return false
  }

  // 支持完整URL
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return true
  }

  // 支持相对路径
  if (imagePath.startsWith('/upload/file/')) {
    return true
  }

  // 支持简单文件名
  if (/\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(imagePath)) {
    return true
  }

  return false
}

/**
 * 从完整URL中提取相对路径
 * 用于数据迁移或兼容处理
 * @param {string} fullUrl 完整URL
 * @returns {string} 相对路径
 */
export function extractRelativePath(fullUrl) {
  if (!fullUrl) {
    return ''
  }

  // 如果已经是相对路径，直接返回
  if (fullUrl.startsWith('/')) {
    return fullUrl
  }

  try {
    const url = new URL(fullUrl)
    return url.pathname
  } catch (e) {
    // 如果URL格式不正确，尝试简单的字符串处理
    const match = fullUrl.match(/\/upload\/file\/.+$/)
    return match ? match[0] : fullUrl
  }
}

/**
 * 批量处理图片URL数组
 * @param {Array<string>} imageArray 图片路径数组
 * @returns {Array<string>} 处理后的图片URL数组
 */
export function processImageArray(imageArray) {
  if (!Array.isArray(imageArray)) {
    return []
  }

  return imageArray
    .filter(path => isValidImagePath(path))
    .map(path => getImageUrl(path))
}

// 默认导出主要函数
export default {
  getImageUrl,
  getImageUrls,
  isValidImagePath,
  extractRelativePath,
  processImageArray
}
